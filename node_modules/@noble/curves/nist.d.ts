import { type CurveFnWithCreate } from './_shortw_utils.ts';
import { type H2CHasher } from './abstract/hash-to-curve.ts';
/** NIST P256 (aka secp256r1, prime256v1) curve, ECDSA and ECDH methods. */
export declare const p256: CurveFnWithCreate;
/** Alias to p256. */
export declare const secp256r1: CurveFnWithCreate;
/** Hashing / encoding to p256 points / field. RFC 9380 methods. */
export declare const p256_hasher: H2CHasher<bigint>;
/** NIST P384 (aka secp384r1) curve, ECDSA and ECDH methods. */
export declare const p384: CurveFnWithCreate;
/** Alias to p384. */
export declare const secp384r1: CurveFnWithCreate;
/** Hashing / encoding to p384 points / field. RFC 9380 methods. */
export declare const p384_hasher: H2CHasher<bigint>;
/** NIST P521 (aka secp521r1) curve, ECDSA and ECDH methods. */
export declare const p521: CurveFnWithCreate;
/** Alias to p521. */
export declare const secp521r1: CurveFnWithCreate;
/** Hashing / encoding to p521 points / field. RFC 9380 methods. */
export declare const p521_hasher: H2CHasher<bigint>;
//# sourceMappingURL=nist.d.ts.map