import { type CurveFn } from './abstract/bls.ts';
import { type IField } from './abstract/modular.ts';
export declare const bls12_381_Fr: IField<bigint>;
/**
 * bls12-381 pairing-friendly curve.
 * @example
 * import { bls12_381 as bls } from '@noble/curves/bls12-381';
 * // G1 keys, G2 signatures
 * const privateKey = '67d53f170b908cabb9eb326c3c337762d59289a8fec79f7bc9254b584b73265c';
 * const message = '64726e3da8';
 * const publicKey = bls.getPublicKey(privateKey);
 * const signature = bls.sign(message, privateKey);
 * const isValid = bls.verify(signature, message, publicKey);
 */
export declare const bls12_381: CurveFn;
//# sourceMappingURL=bls12-381.d.ts.map