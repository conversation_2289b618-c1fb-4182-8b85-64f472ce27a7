{"version": 3, "file": "curve.d.ts", "sourceRoot": "", "sources": ["../../src/abstract/curve.ts"], "names": [], "mappings": "AAOA,OAAO,EAAwB,KAAK,MAAM,EAA0B,MAAM,cAAc,CAAC;AAKzF,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI;IAC3B,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,CAAC;CACN,GAAG;IAAE,CAAC,CAAC,EAAE,KAAK,CAAC;IAAC,CAAC,CAAC,EAAE,KAAK,CAAA;CAAE,CAAC;AAE7B,MAAM,WAAW,KAAK,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC;IACvC,MAAM,IAAI,CAAC,CAAC;IACZ,MAAM,IAAI,CAAC,CAAC;IACZ,GAAG,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACjB,QAAQ,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACtB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO,CAAC;IAC1B,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;IAC5B,QAAQ,CAAC,CAAC,SAAS,CAAC,EAAE,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;CAC9C;AAED,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAAI;IAChC,IAAI,EAAE,CAAC,CAAC;IACR,IAAI,EAAE,CAAC,CAAC;CACT,CAAC;AACF,MAAM,MAAM,wBAAwB,CAAC,CAAC,IAAI,gBAAgB,CAAC,CAAC,CAAC,GAAG;IAC9D,EAAE,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;IAChB,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;IACnB,UAAU,CAAC,EAAE,EAAE,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACrC,CAAC;AACF,MAAM,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC;AAExC,wBAAgB,QAAQ,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,CAG3E;AAED;;;;;GAKG;AACH,wBAAgB,UAAU,CAAC,CAAC,EAC1B,CAAC,EAAE,wBAAwB,CAAC,CAAC,CAAC,EAC9B,QAAQ,EAAE,IAAI,GAAG,IAAI,EACrB,MAAM,EAAE,CAAC,EAAE,GACV,CAAC,EAAE,CAML;AAOD,uDAAuD;AACvD,MAAM,MAAM,KAAK,GAAG;IAClB,OAAO,EAAE,MAAM,CAAC;IAChB,UAAU,EAAE,MAAM,CAAC;IACnB,IAAI,EAAE,MAAM,CAAC;IACb,SAAS,EAAE,MAAM,CAAC;IAClB,OAAO,EAAE,MAAM,CAAC;CACjB,CAAC;AAgEF,MAAM,MAAM,KAAK,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,IAAI;IACtC,eAAe,EAAE,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC;IACxE,cAAc,CAAC,GAAG,EAAE,CAAC,GAAG,OAAO,CAAC;IAChC,YAAY,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAC1C,gBAAgB,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;IAChD,cAAc,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;IAC5D,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,GAAG;QAAE,CAAC,EAAE,CAAC,CAAC;QAAC,CAAC,EAAE,CAAC,CAAA;KAAE,CAAC;IAC7D,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IAC/D,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG;QAAE,CAAC,EAAE,CAAC,CAAC;QAAC,CAAC,EAAE,CAAC,CAAA;KAAE,CAAC;IACnE,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;IACtE,aAAa,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,CAAC;CACtC,CAAC;AAEF;;;;;;;;;;;;;GAaG;AACH,wBAAgB,IAAI,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAyJvF;AAED;;;GAGG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAC9C,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EACtB,KAAK,EAAE,CAAC,EACR,EAAE,EAAE,MAAM,EACV,EAAE,EAAE,MAAM,GACT;IAAE,EAAE,EAAE,CAAC,CAAC;IAAC,EAAE,EAAE,CAAC,CAAA;CAAE,CAYlB;AAED;;;;;;;;;GASG;AACH,wBAAgB,SAAS,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EAC1C,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EACtB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EACtB,MAAM,EAAE,CAAC,EAAE,EACX,OAAO,EAAE,MAAM,EAAE,GAChB,CAAC,CAwCH;AACD;;;;;;GAMG;AACH,wBAAgB,mBAAmB,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,EACpD,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAAC,EACtB,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,EACtB,MAAM,EAAE,CAAC,EAAE,EACX,UAAU,EAAE,MAAM,GACjB,CAAC,OAAO,EAAE,MAAM,EAAE,KAAK,CAAC,CAoE1B;AAED;;;GAGG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,IAAI;IAC1B,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,EAAE,MAAM,CAAC;IACV,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,CAAC,EAAE,MAAM,CAAC;IACV,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,EAAE,EAAE,CAAC,CAAC;IACN,EAAE,EAAE,CAAC,CAAC;IACN,kBAAkB,CAAC,EAAE,OAAO,CAAC;CAC9B,CAAC;AAGF,kBAAkB;AAClB,wBAAgB,aAAa,CAAC,EAAE,EAAE,CAAC,EACjC,KAAK,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,CAAC,GACxB,QAAQ,CACT;IACE,QAAQ,CAAC,UAAU,EAAE,MAAM,CAAC;IAC5B,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC;CAC9B,GAAG,UAAU,CAAC,EAAE,CAAC,GAChB,CAAC,GAAG;IACF,CAAC,EAAE,MAAM,CAAC;CACX,CACJ,CAqBA;AAED,MAAM,MAAM,gBAAgB,CAAC,CAAC,IAAI;IAChC,CAAC,EAAE,CAAC,CAAC;IACL,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,CAAC,EAAE,MAAM,CAAC;IACV,EAAE,EAAE,CAAC,CAAC;IACN,EAAE,EAAE,CAAC,CAAC;CACP,GAAG,CAAC;IAAE,CAAC,EAAE,CAAC,CAAA;CAAE,GAAG;IAAE,CAAC,EAAE,CAAC,CAAA;CAAE,CAAC,CAAC;AAW1B,MAAM,MAAM,IAAI,CAAC,CAAC,IAAI;IAAE,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;IAAC,EAAE,EAAE,MAAM,CAAC,MAAM,CAAC,CAAA;CAAE,CAAC;AAC5D,8CAA8C;AAC9C,wBAAgB,kBAAkB,CAAC,CAAC,EAClC,IAAI,EAAE,aAAa,GAAG,SAAS,EAC/B,KAAK,EAAE,gBAAgB,CAAC,CAAC,CAAC,EAC1B,SAAS,GAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAM,GAC/B,IAAI,CAAC,CAAC,CAAC,CAiBT"}