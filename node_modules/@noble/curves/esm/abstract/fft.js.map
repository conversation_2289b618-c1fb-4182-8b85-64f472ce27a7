{"version": 3, "file": "fft.js", "sourceRoot": "", "sources": ["../../src/abstract/fft.ts"], "names": [], "mappings": "AAcA,SAAS,QAAQ,CAAC,CAAS;IACzB,gBAAgB;IAChB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,UAAU;QACrD,MAAM,IAAI,KAAK,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC;IAC5C,OAAO,CAAC,CAAC;AACX,CAAC;AAED,+CAA+C;AAC/C,MAAM,UAAU,YAAY,CAAC,CAAS;IACpC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,CAAS;IACtC,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI,CAAC,IAAI,CAAC;QAAE,OAAO,CAAC,CAAC;IACrB,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AACxC,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,CAAS,EAAE,IAAY;IACjD,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,IAAI,QAAQ,GAAG,CAAC,CAAC;IACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC;QAAE,QAAQ,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9E,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,gFAAgF;AAChF,MAAM,UAAU,IAAI,CAAC,CAAS;IAC5B,QAAQ,CAAC,CAAC,CAAC,CAAC;IACZ,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,kBAAkB,CAAkC,MAAS;IAC3E,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;IACxB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAC3B,MAAM,IAAI,KAAK,CAAC,iDAAiD,GAAG,CAAC,CAAC,CAAC;IACzE,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3B,MAAM,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACV,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;YACtB,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAClB,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,MAAM,UAAU,sBAAsB,CAAI,MAAW;IACnD,OAAO,kBAAkB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAQ,CAAC;AACnD,CAAC;AAED,MAAM,GAAG,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvC,SAAS,aAAa,CAAC,KAAqB;IAC1C,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;IAClB,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,IAAI,GAAG,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;QAAC,CAAC;IACpE,OAAO,CAAC,CAAC;AACX,CAAC;AASD,wFAAwF;AACxF,MAAM,UAAU,YAAY,CAAC,KAAqB,EAAE,SAAkB;IACpE,mDAAmD;IACnD,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC;IAClC,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,OAAO,CAAC,SAAS,GAAG,GAAG,CAAC,KAAK,GAAG,EAAE,UAAU,EAAE,EAAE,SAAS,KAAK,GAAG;QAAC,CAAC;IAEnE,6BAA6B;IAC7B,IAAI,CAAC,GAAG,SAAS,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;IAC3E,sBAAsB;IACtB,MAAM,MAAM,GAAa,IAAI,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACnD,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;IAC7C,KAAK,IAAI,CAAC,GAAG,UAAU,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;QAAE,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1E,uDAAuD;IACvD,MAAM,UAAU,GAAe,EAAE,CAAC;IAClC,MAAM,SAAS,GAAG,CAAC,IAAY,EAAE,EAAE;QACjC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,IAAI,IAAI,GAAG,EAAE,IAAI,IAAI,GAAG,UAAU;YAChC,MAAM,IAAI,KAAK,CAAC,2BAA2B,GAAG,IAAI,GAAG,cAAc,GAAG,UAAU,CAAC,CAAC;QACpF,OAAO,IAAI,CAAC;IACd,CAAC,CAAC;IACF,MAAM,eAAe,GAAG,CAAC,QAAgB,EAAE,EAAE;QAC3C,SAAS,CAAC,QAAQ,CAAC,CAAC;QACpB,KAAK,IAAI,KAAK,GAAG,QAAQ,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,EAAE,EAAE,CAAC;YAC/C,IAAI,UAAU,CAAC,KAAK,CAAC;gBAAE,SAAS,CAAC,sDAAsD;YACvF,MAAM,YAAY,GAAa,EAAE,CAAC;YAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;gBACvF,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzB,UAAU,CAAC,KAAK,CAAC,GAAG,YAAY,CAAC;QACnC,CAAC;QACD,OAAO,UAAU,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC,CAAC;IACF,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAoB,CAAC;IAC7C,MAAM,YAAY,GAAG,IAAI,GAAG,EAAoB,CAAC;IAEjD,+DAA+D;IAC/D,kDAAkD;IAClD,OAAO;QACL,KAAK,EAAE,CAAC,IAAY,EAAY,EAAE;YAChC,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,OAAO,eAAe,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QACD,GAAG,CAAC,IAAY;YACd,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAE,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC;iBACxC,CAAC;gBACJ,MAAM,GAAG,GAAG,sBAAsB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClD,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACrB,OAAO,GAAG,CAAC;YACb,CAAC;QACH,CAAC;QACD,OAAO,CAAC,IAAY;YAClB,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;YAC1B,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC;gBAAE,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC;iBAChD,CAAC;gBACJ,MAAM,GAAG,GAAG,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;gBACzB,OAAO,GAAG,CAAC;YACb,CAAC;QACH,CAAC;QACD,KAAK,EAAE,CAAC,IAAY,EAAU,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QACxD,KAAK,EAAE,GAAS,EAAE;YAChB,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;YACxC,QAAQ,CAAC,KAAK,EAAE,CAAC;QACnB,CAAC;KACF,CAAC;AACJ,CAAC;AA4BD;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,CAAO,CAAgB,EAAE,QAAwB,EAAkB,EAAE;IAC1F,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,iBAAiB,GAAG,KAAK,EAAE,UAAU,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,EAAE,GAAG,QAAQ,CAAC;IAC1F,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;IACrB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;IACrF,MAAM,KAAK,GAAG,GAAG,KAAK,iBAAiB,CAAC;IACxC,KAAK,CAAC;IACN,OAAO,CAA0B,MAAS,EAAK,EAAE;QAC/C,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;QACzE,IAAI,GAAG,IAAI,GAAG;YAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,4CAA4C;YAC5C,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;YAC9C,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACjB,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;YAClB,MAAM,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC;YACtB,sCAAsC;YACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC9B,+CAA+C;gBAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;oBACvC,MAAM,OAAO,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;oBACvE,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;oBACjB,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;oBACtB,MAAM,KAAK,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC;oBAC7B,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;oBACrB,MAAM,CAAC,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;oBACrB,mDAAmD;oBACnD,IAAI,KAAK,EAAE,CAAC;wBACV,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,yBAAyB;wBACpD,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBACzB,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC3B,CAAC;yBAAM,IAAI,iBAAiB,EAAE,CAAC;wBAC7B,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,iDAAiD;wBAC3E,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACzC,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,yBAAyB;wBACnD,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;oBACzC,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QACD,IAAI,CAAC,GAAG,IAAI,GAAG;YAAE,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC;AACJ,CAAC,CAAC;AAOF;;;GAGG;AACH,MAAM,UAAU,GAAG,CAAI,KAAmB,EAAE,IAAwB;IAClE,MAAM,OAAO,GAAG,CACd,CAAS,EACT,KAAyB,EACzB,QAAQ,GAAG,KAAK,EAChB,SAAS,GAAG,KAAK,EAC4B,EAAE;QAC/C,IAAI,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC1B,2DAA2D;YAC3D,OAAO,CAAC,MAAM,EAAE,EAAE,CAChB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC;QACpF,CAAC;QACD,IAAI,QAAQ;YAAE,OAAO,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;QACxE,IAAI,SAAS;YAAE,OAAO,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,cAAc;IAC1E,CAAC,CAAC;IACF,OAAO;QACL,MAAM,CAA0B,MAAS,EAAE,QAAQ,GAAG,KAAK,EAAE,SAAS,GAAG,KAAK;YAC5E,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;YACxB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;YACrF,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,OAAO,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAI,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;QAC/E,CAAC;QACD,OAAO,CAA0B,MAAS,EAAE,QAAQ,GAAG,KAAK,EAAE,SAAS,GAAG,KAAK;YAC7E,MAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;YACxB,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YACrB,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YACjF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ;YACrD,sDAAsD;YACtD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YACpE,2DAA2D;YAC3D,kDAAkD;YAClD,qFAAqF;YACrF,OAAO,GAAG,CAAC;QACb,CAAC;KACF,CAAC;AACJ,CAAC;AA0DD,MAAM,UAAU,IAAI,CAClB,KAAgB,EAChB,KAAmB,EACnB,MAA2B,EAC3B,GAAmB,EACnB,MAAe;IAEf,MAAM,CAAC,GAAG,KAAK,CAAC;IAChB,MAAM,OAAO,GACX,MAAM;QACL,CAAC,CAAC,GAAW,EAAE,GAAO,EAAiB,EAAE,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,IAAI,CAAC,CAG3E,CAAC;IAEL,MAAM,MAAM,GAAG,CAAC,CAAM,EAAU,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC7E,MAAM,WAAW,GAAG,CAAC,GAAG,GAAQ,EAAU,EAAE;QAC1C,IAAI,CAAC,GAAG,CAAC,MAAM;YAAE,OAAO,CAAC,CAAC;QAC1B,KAAK,MAAM,CAAC,IAAI,GAAG;YAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,GAAG,CAAC,CAAC,CAAC;QACnF,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;QACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;YACjC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC;gBAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;QAChG,IAAI,MAAM,KAAK,SAAS,IAAI,CAAC,KAAK,MAAM;YACtC,MAAM,IAAI,KAAK,CAAC,+BAA+B,MAAM,SAAS,CAAC,EAAE,CAAC,CAAC;QACrE,OAAO,CAAC,CAAC;IACX,CAAC,CAAC;IACF,SAAS,cAAc,CAAC,CAAI,EAAE,CAAS,EAAE,GAAG,GAAG,KAAK;QAClD,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACrB,MAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;YAAE,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAM,CAAC;gBAAE,OAAO,CAAC,CAAC;QAClE,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IACD,0CAA0C;IAC1C,OAAO;QACL,KAAK;QACL,MAAM,EAAE,OAAO;QACf,MAAM;QACN,MAAM,EAAE,CAAC,CAAI,EAAE,GAAW,EAAK,EAAE;YAC/B,WAAW,CAAC,CAAC,CAAC,CAAC;YACf,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;YACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,OAAO,GAAG,CAAC;QACb,CAAC;QACD,MAAM,EAAE,CAAC,CAAI,EAAU,EAAE;YACvB,WAAW,CAAC,CAAC,CAAC,CAAC;YACf,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;gBAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAAE,OAAO,CAAC,CAAC;YACnE,OAAO,CAAC,CAAC,CAAC;QACZ,CAAC;QACD,GAAG,EAAE,CAAC,CAAI,EAAE,CAAI,EAAK,EAAE;YACrB,MAAM,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC;QACD,GAAG,EAAE,CAAC,CAAI,EAAE,CAAI,EAAK,EAAE;YACrB,MAAM,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC;QACD,GAAG,EAAE,CAAC,CAAI,EAAE,CAAI,EAAK,EAAE;YACrB,MAAM,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;YACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACzD,OAAO,GAAG,CAAC;QACb,CAAC;QACD,GAAG,EAAE,CAAC,CAAI,EAAE,CAAQ,EAAK,EAAE;YACzB,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;gBACd,MAAM,GAAG,GAAG,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9B,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;oBACrC,MAAM,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;oBACrC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;wBAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAM,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACN,+DAA+D;oBAC/D,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;oBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;wBAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;4BAC7B,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,kBAAkB;4BAC3C,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC5C,CAAC;oBACH,CAAC;oBACD,OAAO,GAAG,CAAC;gBACb,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,MAAM,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE;oBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7D,OAAO,GAAG,CAAC;YACb,CAAC;QACH,CAAC;QACD,QAAQ,CAAC,CAAI,EAAE,CAAI;YACjB,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACpD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC;QACD,KAAK,CAAC,CAAI,EAAE,MAAc;YACxB,MAAM,GAAG,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBACjD,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAC7B,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QACD,KAAK,EAAE,CAAC,CAAI,EAAK,EAAE;YACjB,WAAW,CAAC,CAAC,CAAC,CAAC;YACf,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;gBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,OAAO,GAAG,CAAC;QACb,CAAC;QACD,IAAI,EAAE,CAAC,CAAI,EAAE,KAAQ,EAAK,EAAE;YAC1B,WAAW,CAAC,CAAC,CAAC,CAAC;YACf,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC;YACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3E,OAAO,GAAG,CAAC;QACb,CAAC;QACD,QAAQ,EAAE;YACR,KAAK,EAAE,CAAC,CAAI,EAAE,CAAS,EAAK,EAAE;gBAC5B,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAI,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC;gBAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;oBAC3B,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;oBACb,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;gBACtB,CAAC;gBACD,OAAO,GAAG,CAAC;YACb,CAAC;YACD,IAAI,EAAE,CAAC,CAAI,EAAE,CAAI,EAAK,EAAE;gBACtB,WAAW,CAAC,CAAC,CAAC,CAAC;gBACf,yEAAyE;gBACzE,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzE,OAAO,GAAG,CAAC;YACb,CAAC;SACF;QACD,QAAQ,EAAE;YACR,KAAK,EAAE,CAAC,CAAI,EAAE,CAAS,EAAE,GAAG,GAAG,KAAK,EAAE,OAAW,EAAK,EAAE;gBACtD,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACrB,MAAM,KAAK,GAAG,OAAO,IAAI,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;gBAC1F,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACvB,4BAA4B;gBAC5B,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC;gBACtC,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC;oBACf,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;oBACjB,OAAO,GAAG,CAAC;gBACb,CAAC;gBACD,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/B,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAM,CAAC,CAAC,CAAC,CAAC,iBAAiB;gBAC3E,MAAM,KAAK,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;oBAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAM,CAAC,CAAC;gBAC/D,MAAM,GAAG,GAAG,CAAC,CAAC,WAAW,CAAC,KAAmB,CAAC,CAAC;gBAC/C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;oBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAM,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5E,OAAO,GAAG,CAAC;YACb,CAAC;YACD,IAAI,CAAC,CAAI,EAAE,CAAI,EAAE,GAAG,GAAG,KAAK;gBAC1B,WAAW,CAAC,CAAC,CAAC,CAAC;gBACf,MAAM,GAAG,GAAG,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAC7C,IAAI,GAAG,KAAK,CAAC,CAAC;oBAAE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,YAAY;gBAC3C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ;gBAChD,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC;gBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE;oBAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzF,OAAO,GAAG,CAAC;YACb,CAAC;SACF;QACD,SAAS,CAAC,KAAQ;YAChB,WAAW,CAAC,KAAK,CAAC,CAAC;YACnB,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;YAC9C,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;YACf,KAAK,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;gBACtB,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACrB,KAAK,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE;oBAAE,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBACxF,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO,GAAG,CAAC;QACb,CAAC;KACF,CAAC;AACJ,CAAC"}